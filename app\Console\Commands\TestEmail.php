<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Models\User;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:email {email}';

    /**
     * The console command description.
     */
    protected $description = 'Test email functionality by sending a test email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        try {
            Mail::raw('This is a test email from USDTRain. If you receive this, your email configuration is working correctly!', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email - USDTRain')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });
            
            $this->info("Test email sent successfully to: {$email}");
            $this->info("Check your email inbox (and spam folder) for the test message.");
            
        } catch (\Exception $e) {
            $this->error("Failed to send test email: " . $e->getMessage());
            $this->info("Please check your mail configuration in the .env file.");
        }
    }
}
