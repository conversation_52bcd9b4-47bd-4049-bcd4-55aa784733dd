<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Changed - USDTRain</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1c40f;
        }
        .logo {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
        }
        .logo-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #f1c40f, #f39c12);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        .logo-text {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(135deg, #f1c40f, #f39c12);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .tagline {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .content {
            margin-bottom: 30px;
        }
        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .message {
            font-size: 16px;
            color: #555;
            margin-bottom: 25px;
            line-height: 1.7;
        }
        .success-notice {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .success-notice h3 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .success-notice p {
            color: #155724;
            margin: 0;
            font-size: 16px;
        }
        .security-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .security-alert h4 {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .security-alert p {
            color: #856404;
            margin: 0 0 15px 0;
            font-size: 14px;
        }
        .security-alert ul {
            color: #856404;
            margin: 0;
            padding-left: 20px;
        }
        .security-alert li {
            margin-bottom: 8px;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .secure-button {
            display: inline-block;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .secure-button:hover {
            transform: translateY(-2px);
        }
        .contact-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .contact-button:hover {
            transform: translateY(-2px);
        }
        .info-box {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .info-box h4 {
            color: #1565c0;
            margin: 0 0 15px 0;
            font-size: 16px;
        }
        .info-box p {
            color: #1565c0;
            margin: 0 0 10px 0;
            font-size: 14px;
        }
        .info-box .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e1f5fe;
        }
        .info-box .detail-item:last-child {
            border-bottom: none;
        }
        .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .footer a {
            color: #f39c12;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon">
                    <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
                    </svg>
                </div>
                <div>
                    <div class="logo-text">USDTRain</div>
                    <div class="tagline">Investment Platform</div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">Hello {{ $user->first_name ?? $user->name }},</div>
            
            <div class="success-notice">
                <h3>✅ Password Successfully Changed</h3>
                <p>Your USDTRain account password has been updated successfully.</p>
            </div>

            <div class="message">
                This email confirms that your account password was changed on <strong>{{ $timestamp }}</strong>. If you made this change, no further action is required.
            </div>

            <div class="info-box">
                <h4>📋 Change Details</h4>
                <div class="detail-item">
                    <span><strong>Account:</strong></span>
                    <span>{{ $user->email }}</span>
                </div>
                <div class="detail-item">
                    <span><strong>Date & Time:</strong></span>
                    <span>{{ $timestamp }}</span>
                </div>
                <div class="detail-item">
                    <span><strong>IP Address:</strong></span>
                    <span>{{ $ipAddress }}</span>
                </div>
                <div class="detail-item">
                    <span><strong>Device:</strong></span>
                    <span>{{ $userAgent }}</span>
                </div>
            </div>

            <div class="security-alert">
                <h4>
                    <span>🚨</span>
                    <span>Didn't make this change?</span>
                </h4>
                <p>If you did not change your password, your account may have been compromised. Please take immediate action:</p>
                <ul>
                    <li>Reset your password immediately using the button below</li>
                    <li>Review your recent account activity</li>
                    <li>Contact our support team for assistance</li>
                    <li>Consider enabling two-factor authentication</li>
                </ul>
            </div>

            <div class="action-buttons">
                <a href="{{ route('password.request') }}" class="secure-button">
                    🔒 Reset Password Again
                </a>
                <a href="{{ route('contact') }}" class="contact-button">
                    📞 Contact Support
                </a>
            </div>

            <div class="message">
                <strong>Security Tips:</strong><br>
                • Use a strong, unique password for your USDTRain account<br>
                • Never share your login credentials with anyone<br>
                • Always log out from shared or public computers<br>
                • Contact support immediately if you notice any suspicious activity
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                This email was sent by <strong>USDTRain</strong><br>
                Your trusted USDT investment platform
            </p>
            <p style="margin-top: 15px;">
                <a href="{{ route('home') }}">Visit our website</a> | 
                <a href="{{ route('contact') }}">Contact Support</a> |
                <a href="{{ route('login') }}">Login to Your Account</a>
            </p>
            <p style="margin-top: 10px; font-size: 12px; color: #999;">
                &copy; {{ date('Y') }} USDTRain. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
