<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Referral extends Model
{
    use HasFactory;

    protected $fillable = [
        'referrer_id',
        'referee_id',
        'level',
        'investment_amount',
        'commission_rate',
        'commission_amount',
        'status',
        'paid_at'
    ];

    protected $casts = [
        'investment_amount' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'paid_at' => 'datetime'
    ];

    // Relationship to referrer (user who gets commission)
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    // Relationship to referee (user who was referred)
    public function referee()
    {
        return $this->belongsTo(User::class, 'referee_id');
    }

    // Scope for pending referrals
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Scope for approved referrals
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    // Scope for paid referrals
    public function scopePaid($query)
    {
        return $query->whereNotNull('paid_at');
    }

    // Scope by level
    public function scopeLevel($query, $level)
    {
        return $query->where('level', $level);
    }
}