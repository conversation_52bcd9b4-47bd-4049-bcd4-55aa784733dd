<x-app-layout>
    <!-- Referral Program Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 relative overflow-hidden">
        <!-- Background effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div class="absolute top-3/4 right-1/4 w-96 h-96 bg-yellow-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <div class="inline-flex items-center justify-center p-3 bg-purple-500 bg-opacity-20 rounded-full mb-6">
                    <div class="flex items-center justify-center w-10 h-10 bg-purple-500 rounded-full">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-purple-400 to-purple-500 bg-clip-text text-transparent">Referral</span>
                    Program
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    Earn up to 7% commission from your referrals across 5 levels. Build your network and create multiple income streams with our generous referral program.
                </p>
            </div>
        </div>
    </section>

    <!-- Referral Overview -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-purple-50 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Content -->
                <div class="space-y-8">
                    <div>
                        <h2 class="text-4xl font-bold text-gray-900 mb-6">
                            5-Level 
                            <span class="text-purple-600">Commission Structure</span>
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed mb-6">
                            Our multi-level referral program rewards you for building a strong network. Earn commissions not just from your direct referrals, but from their referrals too - up to 5 levels deep.
                        </p>
                        <p class="text-lg text-gray-600 leading-relaxed mb-6">
                            Every time someone in your network makes an investment, you earn a percentage commission instantly credited to your account.
                        </p>
                    </div>

                    <!-- Commission Rates -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Commission Rates by Level</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Level 1 (Direct Referrals)</span>
                                <span class="text-purple-600 font-bold">7%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Level 2</span>
                                <span class="text-blue-600 font-bold">3%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Level 3</span>
                                <span class="text-green-600 font-bold">2%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Level 4</span>
                                <span class="text-yellow-600 font-bold">1.5%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Level 5</span>
                                <span class="text-red-600 font-bold">1%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Visual Network Tree -->
                <div class="relative">
                    <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-3xl p-8 shadow-2xl">
                        <div class="bg-white rounded-2xl p-8">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Your Referral Network</h3>
                            
                            <!-- Network Tree Visualization -->
                            <div class="space-y-6">
                                <!-- Level 1 (You) -->
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-900">YOU</span>
                                </div>
                                
                                <!-- Level 2 -->
                                <div class="flex justify-center space-x-4">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-1">
                                            <span class="text-xs font-bold text-white">L1</span>
                                        </div>
                                        <span class="text-xs text-gray-600">7%</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-1">
                                            <span class="text-xs font-bold text-white">L1</span>
                                        </div>
                                        <span class="text-xs text-gray-600">7%</span>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-1">
                                            <span class="text-xs font-bold text-white">L1</span>
                                        </div>
                                        <span class="text-xs text-gray-600">7%</span>
                                    </div>
                                </div>
                                
                                <!-- Level 3 -->
                                <div class="flex justify-center space-x-2">
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">L2</span>
                                    </div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">L2</span>
                                    </div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">L3</span>
                                    </div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">L4</span>
                                    </div>
                                    <div class="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold text-white">L5</span>
                                    </div>
                                </div>
                                
                                <div class="text-center text-sm text-gray-600">
                                    Earn from all levels in your network
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">
                    How the Referral Program 
                    <span class="text-purple-600">Works</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Start earning referral commissions in just 3 simple steps.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <span class="text-2xl font-bold text-white">1</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Get Your Link</h3>
                    <p class="text-gray-600 leading-relaxed">
                        After registering, you'll receive a unique referral link from your dashboard. Share this link with friends and family.
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <span class="text-2xl font-bold text-white">2</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Share & Invite</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Share your referral link on social media, messaging apps, or directly with people interested in USDT investment.
                    </p>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <span class="text-2xl font-bold text-white">3</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Earn Commissions</h3>
                    <p class="text-gray-600 leading-relaxed">
                        When your referrals invest, you earn instant commissions. The more they invest, the more you earn!
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Earning Examples -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-purple-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">
                    Referral Earning 
                    <span class="text-purple-600">Examples</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    See how much you could earn from referral commissions with different scenarios.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Example 1: Small Network -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-xl font-bold text-white">5</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Small Network</h3>
                        <p class="text-gray-600">5 direct referrals, $500 each</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 1 (5 × $500 × 7%)</span>
                            <span class="font-bold text-purple-600">$175</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 2 (10 × $300 × 3%)</span>
                            <span class="font-bold text-blue-600">$90</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 3 (15 × $200 × 2%)</span>
                            <span class="font-bold text-green-600">$60</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total Commission</span>
                            <span class="font-bold text-yellow-600">$325</span>
                        </div>
                    </div>
                </div>

                <!-- Example 2: Medium Network -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-xl font-bold text-white">20</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Medium Network</h3>
                        <p class="text-gray-600">20 direct referrals, $1K each</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 1 (20 × $1K × 7%)</span>
                            <span class="font-bold text-purple-600">$1,400</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 2 (40 × $800 × 3%)</span>
                            <span class="font-bold text-blue-600">$960</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 3 (60 × $500 × 2%)</span>
                            <span class="font-bold text-green-600">$600</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total Commission</span>
                            <span class="font-bold text-yellow-600">$2,960</span>
                        </div>
                    </div>
                </div>

                <!-- Example 3: Large Network -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-xl font-bold text-white">50</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Large Network</h3>
                        <p class="text-gray-600">50 direct referrals, $2K each</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 1 (50 × $2K × 7%)</span>
                            <span class="font-bold text-purple-600">$7,000</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 2 (100 × $1.5K × 3%)</span>
                            <span class="font-bold text-blue-600">$4,500</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Level 3 (150 × $1K × 2%)</span>
                            <span class="font-bold text-green-600">$3,000</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total Commission</span>
                            <span class="font-bold text-yellow-600">$14,500</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Referral Tools -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">
                    Referral 
                    <span class="text-purple-600">Tools & Resources</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    We provide you with all the tools you need to successfully promote USDTRain and maximize your referral earnings.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Tool 1 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Unique Referral Link</h3>
                    <p class="text-gray-600 text-sm">
                        Get your personalized referral link that tracks all your referrals automatically.
                    </p>
                </div>

                <!-- Tool 2 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Marketing Materials</h3>
                    <p class="text-gray-600 text-sm">
                        Access banners, images, and promotional content to share on social media.
                    </p>
                </div>

                <!-- Tool 3 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Real-time Tracking</h3>
                    <p class="text-gray-600 text-sm">
                        Monitor your referrals, commissions, and network growth in real-time.
                    </p>
                </div>

                <!-- Tool 4 -->
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Dedicated Support</h3>
                    <p class="text-gray-600 text-sm">
                        Get help from our referral specialists to optimize your earning potential.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-purple-500 via-purple-600 to-purple-700 text-white relative overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        </div>
        
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">
                Start Building Your 
                <span class="text-purple-200">Referral Network</span>
            </h2>
            <p class="text-xl md:text-2xl mb-10 opacity-90 leading-relaxed">
                Join USDTRain today and start earning commissions from your referrals immediately.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="{{ route('register') }}" 
                   class="bg-white text-purple-600 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-2xl transform hover:scale-105 flex items-center gap-3">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path>
                    </svg>
                    Join & Get Your Link
                </a>
                <a href="{{ route('contact') }}" 
                   class="border-2 border-white text-white px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-purple-600 transition-all duration-300 backdrop-blur-sm flex items-center gap-3">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                    </svg>
                    Learn More
                </a>
            </div>
        </div>
    </section>
</x-app-layout>
