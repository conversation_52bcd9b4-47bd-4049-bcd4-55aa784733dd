<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::view('/about', 'about')->name('about');
Route::view('/contact', 'contact')->name('contact');

// Service pages
Route::view('/services/usdt-investment', 'services.usdt-investment')->name('services.usdt-investment');
Route::view('/services/daily-returns', 'services.daily-returns')->name('services.daily-returns');
Route::view('/services/referral-program', 'services.referral-program')->name('services.referral-program');
Route::view('/services/support', 'services.support')->name('services.support');

// Protected user routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

require __DIR__.'/auth.php';
