<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    protected $commands = [
        // Register your custom commands here
        \App\Console\Commands\ProcessDailyEarnings::class,
        \App\Console\Commands\ProcessMonthlySalaries::class,
        \App\Console\Commands\CheckInvestmentRewards::class,
    ];

    protected function schedule(Schedule $schedule)
    {
        // Process daily earnings at midnight
        $schedule->command('earnings:process')
                 ->dailyAt('00:00')
                 ->timezone('UTC');

        // Process monthly salaries on the first day of the month
        $schedule->command('salaries:process')
                 ->monthlyOn(1, '00:00')
                 ->timezone('UTC');

        // Check for investment rewards every hour
        $schedule->command('rewards:check')
                 ->hourly();
    }

    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}