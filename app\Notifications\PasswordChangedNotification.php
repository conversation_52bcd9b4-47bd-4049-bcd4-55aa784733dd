<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Carbon;

class PasswordChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $ipAddress;
    protected $userAgent;
    protected $timestamp;

    /**
     * Create a new notification instance.
     */
    public function __construct($ipAddress = null, $userAgent = null)
    {
        $this->ipAddress = $ipAddress ?: request()->ip();
        $this->userAgent = $userAgent ?: request()->userAgent();
        $this->timestamp = Carbon::now()->format('F j, Y \a\t g:i A T');
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Password Changed - USDTRain Security Alert')
            ->view('emails.password-changed', [
                'user' => $notifiable,
                'timestamp' => $this->timestamp,
                'ipAddress' => $this->ipAddress,
                'userAgent' => $this->parseUserAgent($this->userAgent),
            ]);
    }

    /**
     * Parse user agent to get readable device/browser info
     */
    private function parseUserAgent($userAgent)
    {
        if (empty($userAgent)) {
            return 'Unknown Device';
        }

        // Simple user agent parsing
        $browser = 'Unknown Browser';
        $os = 'Unknown OS';

        // Detect browser
        if (strpos($userAgent, 'Chrome') !== false) {
            $browser = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $browser = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $browser = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $browser = 'Edge';
        }

        // Detect OS
        if (strpos($userAgent, 'Windows') !== false) {
            $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $os = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $os = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $os = 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $os = 'iOS';
        }

        return "{$browser} on {$os}";
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'message' => 'Password changed successfully',
            'timestamp' => $this->timestamp,
            'ip_address' => $this->ipAddress,
            'user_agent' => $this->userAgent,
        ];
    }
}
