<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Notifications\ResetPasswordNotification;

class User extends Authenticatable
{
    use Notifiable;

    protected $fillable = [
        'first_name',
        'last_name',
        'username',
        'phone',
        'email',
        'password',
        'usdt_wallet',
        'referral_code',
        'referred_by',
        'total_investment',
        'total_earnings',
        'referral_earnings',
        'account_status',
        'is_admin'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'total_investment' => 'decimal:2',
        'total_earnings' => 'decimal:2',
        'referral_earnings' => 'decimal:2',
        'is_admin' => 'boolean'
    ];

    // Relationships
    public function investments()
    {
        return $this->hasMany(Investment::class);
    }

    public function referrals()
    {
        return $this->hasMany(Referral::class, 'referrer_id');
    }

    public function referredBy()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    // Check if user has active investments
    public function hasActiveInvestments()
    {
        return $this->investments()->where('status', 'active')->exists();
    }

    // Get direct referrals count
    public function getDirectReferralsCountAttribute()
    {
        return User::where('referred_by', $this->id)->count();
    }

    // Get full name attribute
    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    // Get name attribute for backward compatibility
    public function getNameAttribute()
    {
        return $this->getFullNameAttribute();
    }

    /**
     * Send the password reset notification.
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }
}