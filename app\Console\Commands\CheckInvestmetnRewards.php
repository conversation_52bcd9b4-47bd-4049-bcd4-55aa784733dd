<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\RewardService;

class CheckInvestmentRewards extends Command
{
    protected $signature = 'rewards:check';
    protected $description = 'Check and process investment rewards for eligible users';

    protected $rewardService;

    public function __construct(RewardService $rewardService)
    {
        parent::__construct();
        $this->rewardService = $rewardService;
    }

    public function handle()
    {
        $this->info('Checking investment rewards...');
        
        // Process rewards using the RewardService
        $results = $this->rewardService->processInvestmentRewards();
        
        $this->info("Rewards processed. {$results['eligible']} users eligible, {$results['paid']} rewards paid.");
        
        // Log the process
        Log::info("Investment rewards processed at " . now() . 
                  ". {$results['eligible']} users eligible, {$results['paid']} rewards paid.");
    }
}