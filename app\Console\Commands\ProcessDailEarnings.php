<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Investment;
use App\Services\InvestmentService;

class ProcessDailyEarnings extends Command
{
    protected $signature = 'earnings:process';
    protected $description = 'Process daily earnings for all active investments';

    protected $investmentService;

    public function __construct(InvestmentService $investmentService)
    {
        parent::__construct();
        $this->investmentService = $investmentService;
    }

    public function handle()
    {
        $this->info('Processing daily earnings...');
        
        // Process earnings using the InvestmentService
        $this->investmentService->processDailyEarnings();
        
        $this->info('Daily earnings processed successfully.');
        
        // Log the process
        \Log::info('Daily earnings processed at ' . now());
    }
}