<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Reward extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'reward_type',
        'amount',
        'requirements_met',
        'paid_at',
        'status'
    ];

    protected $casts = [
        'requirements_met' => 'array',
        'paid_at' => 'datetime'
    ];

    // Relationship to User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Scope for pending rewards
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    // Scope for completed rewards
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}