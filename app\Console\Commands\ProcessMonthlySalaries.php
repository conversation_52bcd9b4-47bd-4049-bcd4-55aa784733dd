<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SalaryService;

class ProcessMonthlySalaries extends Command
{
    protected $signature = 'salaries:process';
    protected $description = 'Process monthly salaries for eligible users';

    protected $salaryService;

    public function __construct(SalaryService $salaryService)
    {
        parent::__construct();
        $this->salaryService = $salaryService;
    }

    public function handle()
    {
        $this->info('Processing monthly salaries...');
        
        // Process salaries using the SalaryService
        $results = $this->salaryService->processSalaries();
        
        $this->info("Salaries processed. {$results['eligible']} users eligible, {$results['paid']} salaries paid.");
        
        // Log the process
        \Log::info("Monthly salaries processed at " . now() . 
                  ". {$results['eligible']} users eligible, {$results['paid']} salaries paid.");
    }
}