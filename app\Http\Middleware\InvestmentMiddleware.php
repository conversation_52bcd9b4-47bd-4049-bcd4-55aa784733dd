<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Investment;

class InvestmentMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Check if user has active investments
        $userId = Auth::id();
        $activeInvestments = Investment::where('user_id', $userId)
            ->where('status', 'active')
            ->exists();

        if (!$activeInvestments) {
            return redirect('/account')->with('error', 'You need an active investment to access this feature.');
        }

        return $next($request);
    }
}