<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Notifications\PasswordChangedNotification;

class TestPasswordChangedEmail extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:password-changed {email}';

    /**
     * The console command description.
     */
    protected $description = 'Test password changed notification email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        // Find user by email or create a test user object
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email {$email} not found.");
            $this->info("Please provide an email address of an existing user.");
            return;
        }
        
        try {
            // Send the password changed notification
            $user->notify(new PasswordChangedNotification(
                '*************', // Test IP
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36' // Test User Agent
            ));
            
            $this->info("Password changed notification sent successfully to: {$email}");
            $this->info("Check your email inbox (and spam folder) for the notification.");
            
        } catch (\Exception $e) {
            $this->error("Failed to send password changed notification: " . $e->getMessage());
            $this->info("Please check your mail configuration in the .env file.");
        }
    }
}
