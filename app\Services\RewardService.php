<?php

namespace App\Services;

use App\Models\User;
use App\Models\Reward;

class RewardService
{
    public function processInvestmentRewards()
    {
        $eligibleUsers = 0;
        $rewardsPaid = 0;
        
        // Get all users who might be eligible for rewards
        $users = User::has('investments')->get();
        
        foreach ($users as $user) {
            // Check if user meets any reward criteria based on their total investment
            $rewardType = $this->checkRewardEligibility($user->total_investment);
            
            if ($rewardType && !$this->hasReceivedReward($user->id, $rewardType)) {
                $eligibleUsers++;
                
                // Create reward record
                $rewardAmount = $this->getRewardAmount($rewardType);
                
                Reward::create([
                    'user_id' => $user->id,
                    'reward_type' => $rewardType,
                    'amount' => $rewardAmount,
                    'requirements_met' => json_encode(['investment_amount' => $user->total_investment]),
                    'paid_at' => now(),
                    'status' => 'completed'
                ]);
                
                // Update user's earnings
                $user->total_earnings += $rewardAmount;
                $user->save();
                
                $rewardsPaid++;
            }
        }
        
        return [
            'eligible' => $eligibleUsers,
            'paid' => $rewardsPaid
        ];
    }
    
    private function checkRewardEligibility($investmentAmount)
    {
        if ($investmentAmount >= 3000) {
            return 'reward_3000';
        } elseif ($investmentAmount >= 1500) {
            return 'reward_1500';
        } elseif ($investmentAmount >= 1000) {
            return 'reward_1000';
        }
        
        return null;
    }
    
    private function getRewardAmount($rewardType)
    {
        $amounts = [
            'reward_1000' => 50,
            'reward_1500' => 75,
            'reward_3000' => 100
        ];
        
        return $amounts[$rewardType] ?? 0;
    }
    
    private function hasReceivedReward($userId, $rewardType)
    {
        return Reward::where('user_id', $userId)
            ->where('reward_type', $rewardType)
            ->exists();
    }
}