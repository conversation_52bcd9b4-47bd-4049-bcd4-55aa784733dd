<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VerifiedMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated and verified
        if (Auth::check() && Auth::user()->email_verified_at) {
            return $next($request);
        }

        // Redirect unverified users to verification notice page
        return redirect('/email/verify')->with('error', 'Please verify your email address to access this page.');
    }
}