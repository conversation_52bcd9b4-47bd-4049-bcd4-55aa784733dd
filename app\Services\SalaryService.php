<?php

namespace App\Services;

use App\Models\User;
use App\Models\Salary;

class SalaryService
{
    public function processSalaries()
    {
        $eligibleUsers = 0;
        $salariesPaid = 0;
        
        // Get all users who might be eligible for salaries
        $users = User::with(['investments', 'referrals'])->get();
        
        foreach ($users as $user) {
            // Check if user meets any salary criteria
            $salaryType = $this->checkSalaryEligibility($user);
            
            if ($salaryType) {
                $eligibleUsers++;
                
                // Create salary record
                $salaryAmount = $this->getSalaryAmount($salaryType);
                
                Salary::create([
                    'user_id' => $user->id,
                    'salary_type' => $salaryType,
                    'amount' => $salaryAmount,
                    'requirements_met' => json_encode($this->getRequirements($salaryType, $user)),
                    'paid_at' => now(),
                    'status' => 'completed'
                ]);
                
                // Update user's earnings
                $user->total_earnings += $salaryAmount;
                $user->save();
                
                $salariesPaid++;
            }
        }
        
        return [
            'eligible' => $eligibleUsers,
            'paid' => $salariesPaid
        ];
    }
    
    private function checkSalaryEligibility(User $user)
    {
        $totalInvestment = $user->total_investment;
        $referrals = $user->referrals()->with('referee')->get();
        
        $count30 = 0;
        $count50 = 0;
        $count25_50 = 0;
        
        foreach ($referrals as $referral) {
            $refereeInvestment = $referral->referee->total_investment;
            
            if ($refereeInvestment >= 30 && $refereeInvestment <= 100) {
                $count30++;
            }
            
            if ($refereeInvestment >= 50) {
                $count50++;
                
                if ($refereeInvestment >= 50) {
                    $count25_50++;
                }
            }
        }
        
        // Check salary conditions
        if ($count30 >= 10 && $count50 >= 5 && $totalInvestment >= 2000) {
            return 'salary_30';
        }
        
        if ($count30 >= 10 && $count50 >= 10 && $totalInvestment >= 5000) {
            return 'salary_100';
        }
        
        if ($count25_50 >= 25 && $totalInvestment >= 8000) {
            return 'salary_200';
        }
        
        return null;
    }
    
    private function getSalaryAmount($salaryType)
    {
        $amounts = [
            'salary_30' => 30,
            'salary_100' => 100,
            'salary_200' => 200
        ];
        
        return $amounts[$salaryType] ?? 0;
    }
    
    private function getRequirements($salaryType, User $user)
    {
        $requirements = [];
        
        switch ($salaryType) {
            case 'salary_30':
                $requirements = [
                    'condition' => '10 people with $30 investment, 5 people with $50 investment, $2000 personal investment',
                    'personal_investment' => $user->total_investment
                ];
                break;
                
            case 'salary_100':
                $requirements = [
                    'condition' => '10 people with $30 investment, 10 people with $50 investment, $5000 personal investment',
                    'personal_investment' => $user->total_investment
                ];
                break;
                
            case 'salary_200':
                $requirements = [
                    'condition' => '25 people with $50 investment, $8000 personal investment',
                    'personal_investment' => $user->total_investment
                ];
                break;
        }
        
        return $requirements;
    }
}