<?php

namespace App\Services;

use App\Models\User;
use App\Models\Referral;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReferralService
{
    protected $commissionLevels = [
        1 => 0.07, // 7%
        2 => 0.05, // 5%
        3 => 0.03, // 3%
        4 => 0.01, // 1%
        5 => 0.01, // 1%
    ];

    protected $maxLevel = 5;

    /**
     * Process referral commissions for a new investment
     *
     * @param User $user The user who made the investment
     * @param float $investmentAmount The amount invested
     * @return void
     */
    public function processReferralCommissions(User $user, float $investmentAmount)
    {
        try {
            DB::beginTransaction();

            $referrerId = $user->referred_by;
            $level = 1;

            // Process commissions up to 5 levels or until no more referrers
            while ($referrerId && $level <= $this->maxLevel) {
                $referrer = User::find($referrerId);
                
                if (!$referrer) {
                    break;
                }

                // Calculate commission for this level
                $commissionRate = $this->commissionLevels[$level];
                $commissionAmount = $investmentAmount * $commissionRate;

                // Create referral record
                Referral::create([
                    'referrer_id' => $referrer->id,
                    'referee_id' => $user->id,
                    'level' => $level,
                    'investment_amount' => $investmentAmount,
                    'commission_rate' => $commissionRate * 100, // Store as percentage
                    'commission_amount' => $commissionAmount,
                    'status' => 'pending'
                ]);

                // Update referrer's stats
                $referrer->referral_earnings += $commissionAmount;
                $referrer->total_earnings += $commissionAmount;
                $referrer->save();

                // Move to next level
                $referrerId = $referrer->referred_by;
                $level++;
            }

            DB::commit();
            Log::info("Referral commissions processed for user {$user->id}, investment: {$investmentAmount}");

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Error processing referral commissions: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get referral statistics for a user
     *
     * @param int $userId
     * @return array
     */
    public function getUserReferralStats(int $userId): array
    {
        $stats = [
            'total_commissions' => 0,
            'level_breakdown' => [],
            'referral_count' => 0,
            'active_referrals' => 0
        ];

        // Initialize level breakdown
        for ($i = 1; $i <= $this->maxLevel; $i++) {
            $stats['level_breakdown'][$i] = [
                'count' => 0,
                'commissions' => 0,
                'amount' => 0
            ];
        }

        // Get all referrals for this user
        $referrals = Referral::where('referrer_id', $userId)
            ->with('referee')
            ->get();

        foreach ($referrals as $referral) {
            $level = $referral->level;
            
            // Update level breakdown
            $stats['level_breakdown'][$level]['count']++;
            $stats['level_breakdown'][$level]['commissions'] += $referral->commission_amount;
            $stats['level_breakdown'][$level]['amount'] += $referral->investment_amount;

            // Update totals
            $stats['total_commissions'] += $referral->commission_amount;
            $stats['referral_count']++;

            // Check if referral is active (has investments)
            if ($referral->referee && $referral->referee->total_investment > 0) {
                $stats['active_referrals']++;
            }
        }

        return $stats;
    }

    /**
     * Get complete referral tree for a user
     *
     * @param int $userId
     * @param int $maxDepth
     * @return array
     */
    public function getReferralTree(int $userId, int $maxDepth = 5): array
    {
        return $this->buildReferralTree($userId, 1, $maxDepth);
    }

    /**
     * Recursively build referral tree
     *
     * @param int $userId
     * @param int $currentLevel
     * @param int $maxLevel
     * @return array
     */
    private function buildReferralTree(int $userId, int $currentLevel, int $maxLevel): array
    {
        if ($currentLevel > $maxLevel) {
            return [];
        }

        $referrals = User::where('referred_by', $userId)
            ->with(['investments', 'referrals'])
            ->get();

        $tree = [];

        foreach ($referrals as $referral) {
            $userData = [
                'id' => $referral->id,
                'name' => $referral->name,
                'email' => $referral->email,
                'total_investment' => $referral->total_investment,
                'join_date' => $referral->created_at,
                'level' => $currentLevel,
                'direct_referrals' => $referral->referrals->count(),
                'children' => $this->buildReferralTree($referral->id, $currentLevel + 1, $maxLevel)
            ];

            $tree[] = $userData;
        }

        return $tree;
    }

    /**
     * Get direct referrals for a user
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDirectReferrals(int $userId)
    {
        return User::where('referred_by', $userId)
            ->with(['investments' => function($query) {
                $query->where('status', 'active');
            }])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Calculate total commissions by level for a user
     *
     * @param int $userId
     * @return array
     */
    public function getCommissionsByLevel(int $userId): array
    {
        $commissions = Referral::where('referrer_id', $userId)
            ->select(
                'level',
                DB::raw('COUNT(*) as referral_count'),
                DB::raw('SUM(commission_amount) as total_commissions'),
                DB::raw('SUM(investment_amount) as total_investment')
            )
            ->groupBy('level')
            ->orderBy('level')
            ->get();

        $result = [];
        foreach ($commissions as $commission) {
            $result[$commission->level] = [
                'referral_count' => $commission->referral_count,
                'total_commissions' => $commission->total_commissions,
                'total_investment' => $commission->total_investment
            ];
        }

        // Fill in missing levels with zeros
        for ($i = 1; $i <= $this->maxLevel; $i++) {
            if (!isset($result[$i])) {
                $result[$i] = [
                    'referral_count' => 0,
                    'total_commissions' => 0,
                    'total_investment' => 0
                ];
            }
        }

        ksort($result);
        return $result;
    }

    /**
     * Get recent referral activity for a user
     *
     * @param int $userId
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentReferralActivity(int $userId, int $limit = 10)
    {
        return Referral::where('referrer_id', $userId)
            ->with(['referee' => function($query) {
                $query->select('id', 'name', 'email');
            }])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Check if a referral relationship would create a cycle
     *
     * @param int $referrerId
     * @param int $refereeId
     * @return bool
     */
    public function wouldCreateCycle(int $referrerId, int $refereeId): bool
    {
        // A user cannot refer themselves
        if ($referrerId === $refereeId) {
            return true;
        }

        // Check if the referee is already in the referrer's upline
        $currentReferrer = $referrerId;
        $level = 1;

        while ($currentReferrer && $level <= $this->maxLevel) {
            $user = User::find($currentReferrer);
            
            if (!$user) {
                break;
            }

            // If we find the referee in the upline, it would create a cycle
            if ($user->id === $refereeId) {
                return true;
            }

            $currentReferrer = $user->referred_by;
            $level++;
        }

        return false;
    }

    /**
     * Generate a unique referral code for a user
     *
     * @param User $user
     * @return string
     */
    public function generateReferralCode(User $user): string
    {
        $code = strtoupper(substr(md5($user->id . $user->email . time()), 0, 8));
        
        // Ensure uniqueness
        while (User::where('referral_code', $code)->exists()) {
            $code = strtoupper(substr(md5(uniqid() . $user->id), 0, 8));
        }

        return $code;
    }

    /**
     * Process pending referral commissions (for manual approval system)
     *
     * @param array $referralIds
     * @return array
     */
    public function approveCommissions(array $referralIds): array
    {
        $approved = 0;
        $failed = 0;

        foreach ($referralIds as $referralId) {
            try {
                $referral = Referral::findOrFail($referralId);
                
                if ($referral->status === 'pending') {
                    $referral->status = 'approved';
                    $referral->paid_at = now();
                    $referral->save();

                    // Create transaction record
                    Transaction::create([
                        'user_id' => $referral->referrer_id,
                        'type' => 'referral_commission',
                        'amount' => $referral->commission_amount,
                        'description' => "Referral commission from level {$referral->level}",
                        'status' => 'completed'
                    ]);

                    $approved++;
                }
            } catch (\Exception $e) {
                Log::error("Failed to approve commission {$referralId}: " . $e->getMessage());
                $failed++;
            }
        }

        return [
            'approved' => $approved,
            'failed' => $failed
        ];
    }
}