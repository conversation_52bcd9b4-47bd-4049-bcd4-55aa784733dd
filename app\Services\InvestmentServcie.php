<?php

namespace App\Services;

use App\Models\Investment;
use App\Models\Package;
use App\Models\Reward;
use App\Models\User;
use App\Services\ReferralService;

class InvestmentService
{
    protected $referralService;

    public function __construct(ReferralService $referralService)
    {
        $this->referralService = $referralService;
    }

    public function createInvestment(User $user, Package $package, $amount)
    {
        // Validate investment amount against package limits
        if ($amount < $package->min_amount || ($package->max_amount && $amount > $package->max_amount)) {
            throw new \Exception('Investment amount does not meet package requirements');
        }

        // Calculate ROI based on package range
        $roi = $this->calculateROI($package, $amount);
        
        // Create investment
        $investment = Investment::create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'amount' => $amount,
            'current_balance' => $amount,
            'roi_percentage' => $roi,
            'total_earned' => 0,
            'cap_reached' => false,
            'status' => 'active'
        ]);

        // Update user's total investment
        $user->total_investment += $amount;
        $user->save();

        // Process referral commissions
        $this->referralService->processReferralCommissions($user, $amount);

        // Check for rewards
        $this->checkForRewards($user, $amount);

        return $investment;
    }

    private function calculateROI(Package $package, $amount)
    {
        // Calculate ROI based on package range and amount
        $range = $package->max_roi - $package->min_roi;
        $position = ($amount - $package->min_amount) / ($package->max_amount - $package->min_amount);
        return $package->min_roi + ($range * $position);
    }

    private function checkForRewards(User $user, $amount)
    {
        // Implement reward logic based on investment amount
        $rewards = [
            1000 => 50,
            1500 => 75,
            3000 => 100
        ];

        foreach ($rewards as $threshold => $rewardAmount) {
            if ($amount >= $threshold) {
                Reward::create([
                    'user_id' => $user->id,
                    'reward_type' => 'investment_bonus',
                    'amount' => $rewardAmount,
                    'requirements_met' => json_encode(['investment_amount' => $amount]),
                    'paid_at' => now()
                ]);
                break;
            }
        }
    }

    public function processDailyEarnings()
    {
        $investments = Investment::where('status', 'active')
            ->where('cap_reached', false)
            ->get();

        foreach ($investments as $investment) {
            $dailyEarning = $investment->current_balance * ($investment->roi_percentage / 100);
            $investment->current_balance += $dailyEarning;
            $investment->total_earned += $dailyEarning;
            
            // Check if cap (2x) is reached
            if ($investment->total_earned >= ($investment->amount * 2)) {
                $investment->cap_reached = true;
                $investment->status = 'completed';
            }
            
            $investment->save();

            // Update user's total earnings
            $user = $investment->user;
            $user->total_earnings += $dailyEarning;
            $user->save();
        }
    }
}