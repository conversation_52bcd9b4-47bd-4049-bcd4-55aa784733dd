<x-app-layout>
    <!-- Daily Returns Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white py-20 relative overflow-hidden">
        <!-- Background effects -->
        <div class="absolute inset-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-green-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div class="absolute top-3/4 right-1/4 w-96 h-96 bg-yellow-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        </div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <div class="inline-flex items-center justify-center p-3 bg-green-500 bg-opacity-20 rounded-full mb-6">
                    <div class="flex items-center justify-center w-10 h-10 bg-green-500 rounded-full">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    <span class="bg-gradient-to-r from-green-400 to-green-500 bg-clip-text text-transparent">Daily</span>
                    Returns
                </h1>
                <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                    Earn consistent daily returns on your USDT investments. Watch your wealth grow every single day with our proven investment strategies.
                </p>
            </div>
        </div>
    </section>

    <!-- Daily Returns Overview -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-green-50 relative overflow-hidden">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Content -->
                <div class="space-y-8">
                    <div>
                        <h2 class="text-4xl font-bold text-gray-900 mb-6">
                            Guaranteed 
                            <span class="text-green-600">Daily Returns</span>
                        </h2>
                        <p class="text-lg text-gray-600 leading-relaxed mb-6">
                            Our daily returns system ensures you earn profits every single day from your USDT investment. Unlike traditional investments that pay monthly or yearly, you see your money grow daily.
                        </p>
                        <p class="text-lg text-gray-600 leading-relaxed mb-6">
                            Returns are calculated and credited to your account automatically at 12:00 UTC daily. You can withdraw your earnings immediately or reinvest them for compound growth.
                        </p>
                    </div>

                    <!-- Return Rates -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-4">Daily Return Rates</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Starter Package</span>
                                <span class="text-green-600 font-bold">1.0% - 1.5%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Standard Package</span>
                                <span class="text-blue-600 font-bold">1.5% - 2.0%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                                <span class="text-gray-700 font-medium">Premium Package</span>
                                <span class="text-purple-600 font-bold">2.0% - 2.2%</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                                <span class="text-gray-700 font-medium">VIP Package</span>
                                <span class="text-yellow-600 font-bold">2.2% - 2.5%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Calculator -->
                <div class="relative">
                    <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-3xl p-8 shadow-2xl">
                        <div class="bg-white rounded-2xl p-8">
                            <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">Daily Returns Calculator</h3>
                            
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Investment Amount (USDT)</label>
                                    <input type="number" id="investment" placeholder="1000" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-semibold text-gray-700 mb-2">Daily Return Rate</label>
                                    <select id="rate" class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                        <option value="1.5">1.5% (Starter)</option>
                                        <option value="2.0" selected>2.0% (Standard)</option>
                                        <option value="2.2">2.2% (Premium)</option>
                                        <option value="2.5">2.5% (VIP)</option>
                                    </select>
                                </div>
                                
                                <div class="bg-gray-50 rounded-xl p-4">
                                    <div class="grid grid-cols-2 gap-4 text-center">
                                        <div>
                                            <div class="text-2xl font-bold text-green-600" id="dailyReturn">$20</div>
                                            <div class="text-sm text-gray-600">Daily Return</div>
                                        </div>
                                        <div>
                                            <div class="text-2xl font-bold text-blue-600" id="monthlyReturn">$600</div>
                                            <div class="text-sm text-gray-600">Monthly Return</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button onclick="calculate()" 
                                        class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-xl font-semibold hover:from-green-600 hover:to-green-700 transition">
                                    Calculate Returns
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How Daily Returns Work -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">
                    How Daily Returns 
                    <span class="text-green-600">Work</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    Our automated system ensures you receive your daily returns consistently and transparently.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Daily Calculation</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Every day at 12:00 UTC, our system automatically calculates your returns based on your investment amount and package rate.
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Instant Credit</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Your daily returns are instantly credited to your account balance. You'll receive an email notification for each credit.
                    </p>
                </div>

                <!-- Step 3 -->
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Withdraw or Reinvest</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Choose to withdraw your daily earnings immediately or reinvest them to compound your returns and grow faster.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Return Examples -->
    <section class="py-20 bg-gradient-to-br from-gray-50 via-white to-green-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">
                    Real Return 
                    <span class="text-green-600">Examples</span>
                </h2>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    See how much you could earn with different investment amounts and time periods.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Example 1 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-xl font-bold text-white">$100</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Small Investment</h3>
                        <p class="text-gray-600">Standard Package (2% daily)</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Daily Return</span>
                            <span class="font-bold text-green-600">$2.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Weekly Return</span>
                            <span class="font-bold text-blue-600">$14.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Monthly Return</span>
                            <span class="font-bold text-purple-600">$60.00</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total After 30 Days</span>
                            <span class="font-bold text-yellow-600">$160.00</span>
                        </div>
                    </div>
                </div>

                <!-- Example 2 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-xl font-bold text-white">$1K</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Medium Investment</h3>
                        <p class="text-gray-600">Premium Package (2.2% daily)</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Daily Return</span>
                            <span class="font-bold text-green-600">$22.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Weekly Return</span>
                            <span class="font-bold text-blue-600">$154.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Monthly Return</span>
                            <span class="font-bold text-purple-600">$660.00</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total After 30 Days</span>
                            <span class="font-bold text-yellow-600">$1,660.00</span>
                        </div>
                    </div>
                </div>

                <!-- Example 3 -->
                <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-lg font-bold text-white">$10K</span>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900">Large Investment</h3>
                        <p class="text-gray-600">VIP Package (2.5% daily)</p>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Daily Return</span>
                            <span class="font-bold text-green-600">$250.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Weekly Return</span>
                            <span class="font-bold text-blue-600">$1,750.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Monthly Return</span>
                            <span class="font-bold text-purple-600">$7,500.00</span>
                        </div>
                        <div class="flex justify-between border-t pt-3">
                            <span class="text-gray-700 font-semibold">Total After 30 Days</span>
                            <span class="font-bold text-yellow-600">$17,500.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-br from-green-500 via-green-600 to-green-700 text-white relative overflow-hidden">
        <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
            <div class="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse animation-delay-2000"></div>
        </div>
        
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">
                Start Earning 
                <span class="text-green-200">Daily Returns</span>
            </h2>
            <p class="text-xl md:text-2xl mb-10 opacity-90 leading-relaxed">
                Don't wait for monthly or yearly returns. Start earning every single day with USDTRain.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="{{ route('register') }}" 
                   class="bg-white text-green-600 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-gray-100 transition-all duration-300 shadow-2xl transform hover:scale-105 flex items-center gap-3">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Start Earning Today
                </a>
                <a href="{{ route('contact') }}" 
                   class="border-2 border-white text-white px-10 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-green-600 transition-all duration-300 backdrop-blur-sm flex items-center gap-3">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                    </svg>
                    Ask Questions
                </a>
            </div>
        </div>
    </section>

    <script>
        function calculate() {
            const investment = parseFloat(document.getElementById('investment').value) || 1000;
            const rate = parseFloat(document.getElementById('rate').value) || 2.0;
            
            const dailyReturn = investment * (rate / 100);
            const monthlyReturn = dailyReturn * 30;
            
            document.getElementById('dailyReturn').textContent = '$' + dailyReturn.toFixed(2);
            document.getElementById('monthlyReturn').textContent = '$' + monthlyReturn.toFixed(2);
        }
        
        // Auto-calculate on page load
        document.addEventListener('DOMContentLoaded', calculate);
        
        // Auto-calculate on input change
        document.getElementById('investment').addEventListener('input', calculate);
        document.getElementById('rate').addEventListener('change', calculate);
    </script>
</x-app-layout>
